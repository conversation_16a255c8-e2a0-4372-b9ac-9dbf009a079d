# compiled output
/dist
node_modules
# service worker，构建时生成
apps/**/dist
packages/**/dist
# Logs
log
*.log
*.log.*
npm-debug.log*
pnpm-debug.log*
yarn-debug.log*
yarn-error.log*
lerna-debug.log*

# OS
.DS_Store

# Tests
/coverage
/.nyc_output

# IDEs and editors
/.idea
.project
.classpath
.c9/
*.launch
.settings/
*.sublime-workspace

# IDE - VSCode
.vscode/*
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json


# ENV
*.local.env


# log
logs

devcloud
devcloud_*

*/**/.umi
*/**/.idea

.kfc*

turboJson.json
*/**/turboJson.json

.vscode/
.turbo/

.next
.kwaipilot
