# Planner
## 项目概述
Planner 是一个基于 LLM 的 PRD 处理和任务规划工具，目前已实现简单的对话功能，但缺乏持久化的数据存储。本方案旨在扩展 Planner 的功能，使其支持项目管理、会话状态、会话历史记录、任务列表、结构化 PRD等数据的持久化存储。
## 系统架构
系统分为前端（Web）、后端（Backend）、数据库（DB）、LLM 服务、IDE 环境和 MCP 服务模块，各模块的主要功能和交互流程如下：
```mermaid
sequenceDiagram
    autonumber
    participant Dev as 开发者
    participant Web as Web前端
    participant LLM as LLM服务
    participant Backend as 后端系统
    participant DB as 数据库
    participant IDE as IDE环境
    participant MCP as MCP服务
    
    Dev->>Web: 输入原始PRD
    Web->>LLM: 传递PRD内容
    LLM-->>Web: 返回缺失信息的询问
    Web-->>Dev: 显示询问
    Dev->>Web: 提供补充信息
    Web->>LLM: 传递补充信息
    LLM-->>Web: 返回结构化PRD和任务列表
    Web->>Backend: 创建会话记录
    Backend->>DB: 存储会话数据
    DB-->>Backend: 确认存储成功
    Backend-->>Web: 返回会话ID
    Web-->>Dev: 展示结果和MCP调用prompt
    
    Dev->>IDE: 复制prompt开始开发
    Note over Dev,IDE: 开发和调试阶段
    
    IDE->>MCP: 调用init获取会话上下文
    MCP->>Backend: 请求会话数据
    Backend->>DB: 读取会话数据
    DB-->>Backend: 返回会话数据
    Backend-->>MCP: 返回会话上下文
    MCP-->>IDE: 显示会话上下文
    
    Note over IDE,MCP: 执行开发任务
    IDE->>MCP: 调用run_task执行任务
    
    Note over IDE,MCP: 完成后更新状态
    IDE->>MCP: 调用update更新部署信息
    MCP->>Backend: 更新会话状态和部署信息
    Backend->>DB: 更新数据库记录
    DB-->>Backend: 确认更新成功
    Backend-->>MCP: 确认状态更新
    MCP-->>IDE: 返回更新结果
    
    Dev->>Web: 查看任务状态
    Web->>Backend: 请求最新状态
    Backend->>DB: 读取状态数据
    DB-->>Backend: 返回状态数据
    Backend-->>Web: 返回已完成状态和页面链接
    Web-->>Dev: 展示任务完成和线上链接
```
## 技术栈
- 前端：React + TypeScript + zustand + Tailwind CSS + CopilotKit
- 后端：Node.js + NestJS + TypeScript
- 数据库：Mysql + TypeORM
- AI Agent： langgraph + langchain
- MCP: @modelcontextprotocol/sdk + Typescript

## 功能模块
### 会话管理
- 每个PRD是一个会话，每个会话包含AI对话视图、任务管理视图。
### AI 交互
- 每个会话支持多轮AI对话。
    * AI响应的信息需要支持更多样的展现
        * interrupt类型：支持展现amis表单，交由用户填写。
        * message类型：支持markdown的展现。
### 任务管理
- 每个会话结束后，会生成一个任务列表。
    - 任务列表支持多种状态：未开始、进行中、已完成。
    - 每个任务支持基于AI重新生成任务内容。
    - 每个任务提供一个mcp调用的prompt，用户可以复制到IDE中使用。
### AI Agent 
- 对prd的解析、结构化、完善，并最终生成可执行的任务列表。
### MCP Server
- 提供与IDE的交互接口，支持任务的执行和状态更新。

## 开发方案
### 前端
- 目录： apps/client-planner
- 进度：完成项目初始化和基本的PRD输入功能，支持与LLM服务交互。
- 任务：
    - 实现会话管理视图，支持创建、删除、切换会话。
    - 实现AI对话视图，支持多轮对话和不同类型的响应展现。
    - 实现任务管理视图，支持任务状态更新和MCP调用提示。
    - 集成zustand进行状态管理。
    - 使用CopilotKit实现与LLM服务的交互和消息的渲染。
### 后端
- 目录： apps/server-ai/plan
- 任务：
    - 
### AI Agent
- 目录： apps/server-ai/plan/graph
- 进度：完成langgraph的基础集成，支持PRD解析和任务生成。
- 任务：
    - 优化前后端任务生成效果。
### MCP Server
- 目录： apps/mcp-planner
- 任务：
    - 项目初始化，封装sync、start、end等tool，调用task接口下载、开发、 更新任务。
