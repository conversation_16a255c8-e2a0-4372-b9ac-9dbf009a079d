/**
 * 节点名称枚举
 * 定义工作流图中所有节点的标识符
 */
export enum NodeNames {
  COORDINATOR = 'coordinator',
  TOOLS = 'tools',
  PRD_PARSER = 'prdParser',
  PRD_FEEDBACK = 'prdFeedbackNode',
  FRONTEND_PLANNER = 'frontendPlanner',
  BACKEND_PLANNER = 'backendPlanner',
  PLANNER_FEEDBACK = 'plannerFeedback',
}

/**
 * 工具名称枚举
 * 定义所有可用工具的标识符
 */
export enum ToolNames {
  HANDOFF_TO_PLANNER = 'handoff_to_planner',
  FETCH_DOCUMENT = 'fetch_document',
}

/**
 * 任务类型枚举
 * 定义任务的技术栈分类
 */
export enum TaskType {
  FRONTEND = 'frontend',
  BACKEND = 'backend',
  INFRASTRUCTURE = 'infrastructure',
}

/**
 * 任务优先级枚举
 */
export enum TaskPriority {
  HIGH = 'high',
  MEDIUM = 'medium',
  LOW = 'low',
}

/**
 * 任务状态枚举
 */
export enum TaskStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  BLOCKED = 'blocked',
}

/**
 * PRD完整性状态枚举
 */
export enum PRDCompleteness {
  COMPLETE = 'complete',
  INCOMPLETE = 'incomplete',
}
