import { tool } from '@langchain/core/tools';
import { z } from 'zod';
import { ToolNames } from '../constants';
import { fetchDocumentWithImages } from '../openapi';

/**
 * 创建所有工具的函数
 * @returns 工具数组
 */
export function createTools() {
  // 定义 handoff_to_planner 工具
  const handoffToPlannerTool = tool(
    ({ reason }: { reason: string }) => {
      console.log('Handoff to planner tool called with reason:', reason);
      return `已转交给规划器处理，原因：${reason}`;
    },
    {
      name: ToolNames.HANDOFF_TO_PLANNER,
      description: '当识别到PRD请求时，将对话转交给规划器处理',
      schema: z.object({
        reason: z.string().describe('转交给规划器的原因说明'),
      }),
    },
  );

  // 定义 fetch_document 工具
  const fetchDocumentTool = tool(
    async ({ url }: { url: string }) => {
      console.log('Fetch document tool called with URL:', url);
      try {
        const { content, images } = await fetchDocumentWithImages(url);
        return {
          success: true,
          content,
          images,
          message: `文档获取成功，内容长度：${content.length}字符，图片数量：${images.length}`,
        };
      } catch (error) {
        console.error('Error fetching document:', error);
        return {
          success: false,
          content: '',
          images: [],
          message: `文档获取失败：${error.message}`,
        };
      }
    },
    {
      name: ToolNames.FETCH_DOCUMENT,
      description: '当用户提供文档链接时，获取文档内容和图片',
      schema: z.object({
        url: z.string().describe('要获取的文档URL地址'),
      }),
    },
  );

  return [handoffToPlannerTool, fetchDocumentTool];
}

/**
 * 获取特定工具
 * @param toolName 工具名称
 * @returns 工具实例或null
 */
export function getTool(toolName: ToolNames) {
  const tools = createTools();
  return tools.find((tool) => tool.name === toolName) || null;
}

/**
 * 验证工具调用参数
 * @param toolName 工具名称
 * @param args 参数对象
 * @returns 验证结果
 */
export function validateToolArgs(toolName: ToolNames, args: any): boolean {
  try {
    switch (toolName) {
      case ToolNames.HANDOFF_TO_PLANNER:
        return typeof args.reason === 'string' && args.reason.length > 0;
      case ToolNames.FETCH_DOCUMENT:
        return typeof args.url === 'string' && args.url.startsWith('http');
      default:
        return false;
    }
  } catch (error) {
    console.error('Tool args validation error:', error);
    return false;
  }
}
