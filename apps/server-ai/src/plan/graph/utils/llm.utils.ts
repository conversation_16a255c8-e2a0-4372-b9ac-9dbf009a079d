import { ChatOpenAI } from '@langchain/openai';

/**
 * 创建LLM实例的配置接口
 */
interface LLMConfig {
  model?: string;
  apiKey?: string;
  timeout?: number;
  maxRetries?: number;
  baseURL?: string;
  temperature?: number;
}

/**
 * 默认LLM配置
 */
const DEFAULT_LLM_CONFIG: LLMConfig = {
  model: 'gpt-4o',
  apiKey: 'TXI1OjwcUJORWR',
  timeout: 60000, // 60秒超时
  maxRetries: 3, // 最多重试3次
  baseURL: 'https://ai-gateway.corp.kuaishou.com/v2',
  temperature: 0.1, // 降低随机性，提高一致性
};

/**
 * 创建LLM实例
 * @param config 可选的配置覆盖
 * @returns ChatOpenAI实例
 */
export function createLLM(config: Partial<LLMConfig> = {}): ChatOpenAI {
  const finalConfig = { ...DEFAULT_LLM_CONFIG, ...config };

  return new ChatOpenAI({
    model: finalConfig.model,
    apiKey: finalConfig.apiKey,
    timeout: finalConfig.timeout,
    maxRetries: finalConfig.maxRetries,
    temperature: finalConfig.temperature,
    configuration: {
      baseURL: finalConfig.baseURL,
      defaultHeaders: {
        'x-dmo-provider': 'openai',
      },
    },
  });
}

/**
 * 创建用于结构化输出的LLM实例
 * 使用更低的温度以确保输出格式的一致性
 * @param config 可选的配置覆盖
 * @returns ChatOpenAI实例
 */
export function createStructuredLLM(
  config: Partial<LLMConfig> = {},
): ChatOpenAI {
  return createLLM({
    temperature: 0.0, // 完全确定性输出
    ...config,
  });
}

/**
 * 创建用于创意任务的LLM实例
 * 使用更高的温度以增加创造性
 * @param config 可选的配置覆盖
 * @returns ChatOpenAI实例
 */
export function createCreativeLLM(config: Partial<LLMConfig> = {}): ChatOpenAI {
  return createLLM({
    temperature: 0.7, // 增加创造性
    ...config,
  });
}

/**
 * 验证LLM配置
 * @param config LLM配置
 * @returns 验证结果
 */
export function validateLLMConfig(config: LLMConfig): boolean {
  try {
    if (!config.apiKey || config.apiKey.length === 0) {
      console.error('LLM配置错误：缺少API密钥');
      return false;
    }

    if (!config.baseURL || !config.baseURL.startsWith('http')) {
      console.error('LLM配置错误：无效的baseURL');
      return false;
    }

    if (config.timeout && config.timeout < 1000) {
      console.error('LLM配置错误：超时时间过短');
      return false;
    }

    return true;
  } catch (error) {
    console.error('LLM配置验证失败:', error);
    return false;
  }
}

/**
 * 获取LLM健康状态
 * @param llm LLM实例
 * @returns 健康状态检查结果
 */
export async function checkLLMHealth(llm: ChatOpenAI): Promise<boolean> {
  try {
    const response = await llm.invoke('测试连接');
    return response && response.content && response.content.length > 0;
  } catch (error) {
    console.error('LLM健康检查失败:', error);
    return false;
  }
}
