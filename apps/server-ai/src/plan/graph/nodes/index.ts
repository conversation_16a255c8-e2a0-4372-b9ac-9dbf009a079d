/**
 * 节点模块统一导出
 * 提供所有工作流节点的统一入口
 */

// 核心节点
import { coordinatorNode } from './coordinator.node';
import { toolsNode } from './tools.node';
import { prdParserNode } from './prd-parser.node';

// 规划节点
import { frontendPlannerNode } from './frontend-planner.node';
import { backendPlannerNode } from './backend-planner.node';

// 反馈节点
import { prdFeedbackNode, plannerFeedbackNode } from './feedback.nodes';

// 重新导出所有节点函数
export { coordinatorNode } from './coordinator.node';
export { toolsNode } from './tools.node';
export { prdParserNode } from './prd-parser.node';
export { frontendPlannerNode } from './frontend-planner.node';
export { backendPlannerNode } from './backend-planner.node';

export { prdFeedbackNode, plannerFeedbackNode } from './feedback.nodes';

/**
 * 节点映射表
 * 用于动态获取节点函数
 */
export const NODE_MAP = {
  coordinator: coordinatorNode,
  tools: toolsNode,
  prdParser: prdParserNode,
  frontendPlanner: frontendPlannerNode,
  backendPlanner: backendPlannerNode,

  prdFeedbackNode: prdFeedbackNode,
  plannerFeedback: plannerFeedbackNode,
} as const;

/**
 * 获取节点函数
 * @param nodeName 节点名称
 * @returns 节点函数或null
 */
export function getNodeFunction(nodeName: string) {
  return NODE_MAP[nodeName as keyof typeof NODE_MAP] || null;
}

/**
 * 验证节点是否存在
 * @param nodeName 节点名称
 * @returns 是否存在
 */
export function isValidNode(nodeName: string): boolean {
  return nodeName in NODE_MAP;
}

/**
 * 获取所有节点名称
 * @returns 节点名称数组
 */
export function getAllNodeNames(): string[] {
  return Object.keys(NODE_MAP);
}
