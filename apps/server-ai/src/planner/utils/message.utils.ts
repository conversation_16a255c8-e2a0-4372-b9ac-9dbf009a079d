import { AIMessage } from '@langchain/core/messages';

/**
 * 消息处理工具类
 * 统一处理LangGraph中的消息创建和格式化
 */

export interface MessageOptions {
  content: string;
  name: string;
  metadata?: Record<string, any>;
}

export class MessageUtils {
  /**
   * 创建AI消息
   */
  static createAIMessage(options: MessageOptions): AIMessage {
    return new AIMessage({
      content: options.content,
      name: options.name,
      additional_kwargs: options.metadata || {},
    });
  }

  /**
   * 创建带有特定格式的响应消息
   */
  static createResponseMessage(
    content: string,
    agent: string,
    metadata?: Record<string, any>,
  ): AIMessage {
    return this.createAIMessage({
      content,
      name: agent,
      metadata,
    });
  }

  /**
   * 创建错误消息
   */
  static createErrorMessage(
    error: string,
    agent: string,
    errorCode?: string,
  ): AIMessage {
    return this.createAIMessage({
      content: `错误: ${error}`,
      name: agent,
      metadata: {
        type: 'error',
        errorCode,
        timestamp: new Date().toISOString(),
      },
    });
  }

  /**
   * 创建状态更新消息
   */
  static createStatusMessage(
    status: string,
    agent: string,
    details?: Record<string, any>,
  ): AIMessage {
    return this.createAIMessage({
      content: `状态更新: ${status}`,
      name: agent,
      metadata: {
        type: 'status',
        details,
        timestamp: new Date().toISOString(),
      },
    });
  }

  /**
   * 格式化用户友好的消息内容
   */
  static formatUserMessage(
    message: string,
    context?: {
      isComplete?: boolean;

      nextStep?: string;
    },
  ): string {
    let formattedMessage = message;

    if (context?.nextStep) {
      formattedMessage += `\n\n下一步：${context.nextStep}`;
    }

    return formattedMessage;
  }

  /**
   * 提取消息内容（处理不同类型的content）
   */
  static extractContent(content: any): string {
    if (typeof content === 'string') {
      return content;
    }

    if (content && typeof content.toString === 'function') {
      return content.toString();
    }

    return JSON.stringify(content);
  }

  /**
   * 验证消息内容是否有效
   */
  static validateMessageContent(content: any): boolean {
    if (!content) return false;

    const extractedContent = this.extractContent(content);
    return extractedContent.trim().length > 0;
  }

  /**
   * 创建中断反馈消息
   */
  static createInterruptMessage(
    question: string,
    data: Record<string, any>,
    instructions?: string,
  ): Record<string, any> {
    return {
      question,
      ...data,
      instructions: instructions || '请提供反馈以继续处理',
      timestamp: new Date().toISOString(),
    };
  }

  /**
   * 解析反馈消息
   */
  static parseFeedbackMessage(feedback: any): {
    action: 'approve' | 'revise' | 'unknown';
    content: string;
    details?: Record<string, any>;
  } {
    if (!feedback) {
      return { action: 'unknown', content: '' };
    }

    const feedbackStr =
      typeof feedback === 'string' ? feedback : JSON.stringify(feedback);

    let action: 'approve' | 'revise' | 'unknown' = 'unknown';
    if (feedbackStr.includes('approve')) {
      action = 'approve';
    } else if (feedbackStr.includes('revise')) {
      action = 'revise';
    }

    return {
      action,
      content: feedbackStr,
      details: typeof feedback === 'object' ? feedback : undefined,
    };
  }
}
