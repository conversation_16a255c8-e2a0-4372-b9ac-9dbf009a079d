/**
 * XML解析工具类
 * 用于统一处理LLM响应中的XML内容解析
 */

export interface ParsedXMLResponse {
  evaluation?: {
    isPRDRequest?: boolean;
    completeness?: 'COMPLETE' | 'INCOMPLETE';
    status?: string;
  };
  content?: {
    message?: string;
    prd?: string;
    taskJson?: string;
    structuredPrd?: string;
    summary?: {
      totalTasks?: number;
      estimatedDuration?: string;
      keyFeatures?: string[];
    };
  };
}

export class XMLParserUtils {
  /**
   * 解析LLM响应中的XML内容
   */
  static parseXMLResponse(xmlContent: string): ParsedXMLResponse {
    const result: ParsedXMLResponse = {};

    // 解析evaluation部分
    const evaluationMatch = xmlContent.match(
      /<evaluation>(.*?)<\/evaluation>/s,
    );
    if (evaluationMatch) {
      result.evaluation = this.parseEvaluation(evaluationMatch[1]);
    }

    // 解析content部分
    const contentMatch = xmlContent.match(/<content>(.*?)<\/content>/s);
    if (contentMatch) {
      result.content = this.parseContent(contentMatch[1]);
    }

    return result;
  }

  /**
   * 解析evaluation部分
   */
  private static parseEvaluation(evaluationContent: string) {
    const evaluation: any = {};

    const isPRDMatch = evaluationContent.match(
      /<is_prd_request>(.*?)<\/is_prd_request>/,
    );
    if (isPRDMatch) {
      evaluation.isPRDRequest = isPRDMatch[1].trim().toLowerCase() === 'true';
    }

    const completenessMatch = evaluationContent.match(
      /<completeness>(.*?)<\/completeness>/,
    );
    if (completenessMatch) {
      evaluation.completeness = completenessMatch[1].trim() as
        | 'COMPLETE'
        | 'INCOMPLETE';
    }

    const statusMatch = evaluationContent.match(/<status>(.*?)<\/status>/);
    if (statusMatch) {
      evaluation.status = statusMatch[1].trim();
    }

    return evaluation;
  }

  /**
   * 解析content部分
   */
  private static parseContent(contentContent: string) {
    const content: any = {};

    const messageMatch = contentContent.match(/<message>(.*?)<\/message>/s);
    if (messageMatch) {
      content.message = messageMatch[1].trim();
    }

    const prdMatch = contentContent.match(/<prd>(.*?)<\/prd>/s);
    if (prdMatch) {
      content.prd = prdMatch[0]; // 保留完整的prd标签
    }

    const structuredPrdMatch = contentContent.match(
      /<structured_prd>(.*?)<\/structured_prd>/s,
    );
    if (structuredPrdMatch) {
      content.structuredPrd = structuredPrdMatch[1].trim();
    }

    const taskJsonMatch = contentContent.match(
      /<task_json>(.*?)<\/task_json>/s,
    );
    if (taskJsonMatch) {
      content.taskJson = taskJsonMatch[1].trim();
    }

    // 解析summary部分
    const summaryMatch = contentContent.match(/<summary>(.*?)<\/summary>/s);
    if (summaryMatch) {
      content.summary = this.parseSummary(summaryMatch[1]);
    }

    return content;
  }

  /**
   * 解析summary部分
   */
  private static parseSummary(summaryContent: string) {
    const summary: any = {};

    const totalTasksMatch = summaryContent.match(
      /<total_tasks>(.*?)<\/total_tasks>/,
    );
    if (totalTasksMatch) {
      summary.totalTasks = parseInt(totalTasksMatch[1].trim(), 10);
    }

    const estimatedDurationMatch = summaryContent.match(
      /<estimated_duration>(.*?)<\/estimated_duration>/,
    );
    if (estimatedDurationMatch) {
      summary.estimatedDuration = estimatedDurationMatch[1].trim();
    }

    const keyFeaturesMatch = summaryContent.match(
      /<key_features>(.*?)<\/key_features>/s,
    );
    if (keyFeaturesMatch) {
      // 简单解析，假设特性用换行或逗号分隔
      summary.keyFeatures = keyFeaturesMatch[1]
        .trim()
        .split(/[,\n]/)
        .map((f) => f.trim())
        .filter((f) => f.length > 0);
    }

    return summary;
  }

  /**
   * 提取简单的XML标签内容
   */
  static extractTagContent(xmlContent: string, tagName: string): string | null {
    const regex = new RegExp(`<${tagName}>(.*?)<\/${tagName}>`, 's');
    const match = xmlContent.match(regex);
    return match ? match[1].trim() : null;
  }

  /**
   * 检查XML内容是否包含特定标签
   */
  static hasTag(xmlContent: string, tagName: string): boolean {
    const regex = new RegExp(`<${tagName}>.*?<\/${tagName}>`, 's');
    return regex.test(xmlContent);
  }

  /**
   * 验证XML格式是否正确
   */
  static validateXMLStructure(xmlContent: string): boolean {
    try {
      // 简单的XML验证：检查标签是否匹配
      const openTags = xmlContent.match(/<[^\/][^>]*>/g) || [];
      const closeTags = xmlContent.match(/<\/[^>]*>/g) || [];

      // 基本检查：开标签和闭标签数量应该相等
      return openTags.length === closeTags.length;
    } catch (error) {
      return false;
    }
  }
}
