import { <PERSON><PERSON><PERSON> } from '@nestjs/common';
import { ChatController } from './chat.controller';
import { ChatService } from './chat.service';
import { MessageController } from './message.controller';
import { DatabaseModule } from '../../database/database.module';
import { GraphService } from '../../../plan/graph/graph.service';

@Module({
  imports: [DatabaseModule],
  controllers: [ChatController, MessageController],
  providers: [ChatService, GraphService],
  exports: [ChatService],
})
export class ChatModule {}
