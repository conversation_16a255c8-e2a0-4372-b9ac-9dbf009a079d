import { Injectable, NotFoundException } from '@nestjs/common';
import { Observable, Observer } from 'rxjs';
import { v4 as uuidv4 } from 'uuid';
import { DatabaseService } from '../../database/database.service';
import { Message, MessageRole } from '../../database/entities/message.entity';
import { Task, TaskStatus } from '../../database/entities/task.entity';
import { Session, SessionStatus } from '../../database/entities/session.entity';
import { ChatMessage } from './interfaces/chat.interface';
import { logger } from '../../utils/logger';
import { GraphService } from '../../../plan/graph/graph.service';
import { Command } from '@langchain/langgraph';
import { ChatEventBuilder } from './interfaces/chat-events.interface';

// 类型定义
interface WorkflowConfig {
  configurable: {
    thread_id: string;
    mcpSettings: any;
  };
}

// 常量定义
const STREAM_MODES = ['messages', 'updates'] as const;
@Injectable()
export class ChatService {
  private graph: any;

  constructor(
    private readonly databaseService: DatabaseService,
    private readonly graphService: GraphService,
  ) {
    // 获取graph实例
    this.graph = this.graphService.getGraph();
  }

  async streamWorkflow(
    messages: ChatMessage[],
    threadId: string,
    interruptFeedback: string = '',
    mcpSettings: any = {},
  ): Promise<Observable<MessageEvent>> {
    // 如果没有提供threadId，生成一个新的
    if (threadId === '__default__') {
      threadId = uuidv4();
    }

    // 保存用户消息到数据库
    if (messages && messages.length > 0) {
      const lastMessage = messages[messages.length - 1];
      await this.saveMessage({
        threadId,
        role: MessageRole.USER,
        content: lastMessage.content,
      });
    }

    // 创建或更新会话 - 改进逻辑
    await this.createOrUpdateSessionSmart({
      threadId,
      input:
        messages && messages.length > 0
          ? messages[messages.length - 1].content
          : '',
      planIterations: 0,
      status: SessionStatus.REQUIREMENT_ANALYSIS,
      config: {
        mcpSettings,
        interruptFeedback,
      },
    });

    // 创建流式响应
    return new Observable<MessageEvent>((observer) => {
      this.streamWorkflowGenerator(
        messages,
        threadId,
        interruptFeedback,
        mcpSettings,
        observer,
      ).catch((error) => {
        logger.error(`Error in workflow stream: ${error.message}`, error.stack);
        observer.error(error);
      });
    });
  }

  private async saveMessage(message: Partial<Message>): Promise<Message> {
    try {
      return await this.databaseService.createMessage(message);
    } catch (error) {
      logger.error(`Error saving message: ${error.message}`, error.stack);
      throw error;
    }
  }

  /**
   * 智能创建或更新会话 - 避免重复创建
   */
  private async createOrUpdateSessionSmart(
    session: Partial<Session>,
  ): Promise<Session> {
    try {
      logger.log(`尝试创建或更新会话，threadId: ${session.threadId}`);

      // 首先检查是否已存在该threadId的会话
      const existingSession = await this.databaseService.findSessionByThreadId(
        session.threadId,
      );

      if (existingSession) {
        logger.log(`找到现有会话 ${existingSession.id}，更新会话信息`);
        // 如果存在，只更新必要字段，保留原有标题
        return await this.databaseService.updateSession(existingSession.id, {
          input: session.input,
          status: session.status,
          planIterations: (existingSession.planIterations || 0) + 1,
          config: session.config,
        });
      }

      // 如果传入的threadId实际上是sessionId（UUID格式），尝试查找对应的会话
      if (this.isUUID(session.threadId)) {
        logger.log(
          `threadId看起来像sessionId，尝试查找对应会话: ${session.threadId}`,
        );
        const sessionById = await this.databaseService.findSessionById(
          session.threadId,
        );

        if (sessionById) {
          logger.log(`找到对应的会话 ${sessionById.id}，更新threadId`);
          // 如果找到了对应的会话，更新其threadId（如果还没有的话）
          if (!sessionById.threadId) {
            return await this.databaseService.updateSession(sessionById.id, {
              threadId: session.threadId, // 使用sessionId作为threadId
              input: session.input,
              status: session.status,
              planIterations: (sessionById.planIterations || 0) + 1,
              config: session.config,
            });
          } else {
            // 如果已经有threadId，只更新其他字段
            return await this.databaseService.updateSession(sessionById.id, {
              input: session.input,
              status: session.status,
              planIterations: (sessionById.planIterations || 0) + 1,
              config: session.config,
            });
          }
        }
      }

      // 如果没有找到现有会话，创建新会话
      logger.log(`创建新会话，threadId: ${session.threadId}`);
      const sessionData = {
        ...session,
        title: `会话 ${session.threadId.slice(-8)}`, // 使用 threadId 后8位作为默认标题
      };

      return await this.databaseService.createOrUpdateSessionByThreadId(
        sessionData,
      );
    } catch (error) {
      logger.error(
        `Error creating/updating session: ${error.message}`,
        error.stack,
      );
      throw error;
    }
  }

  /**
   * 检查字符串是否为UUID格式
   */
  private isUUID(str: string): boolean {
    const uuidRegex =
      /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
    return uuidRegex.test(str);
  }

  /**
   * 流式工作流生成器 - 优化版本
   */
  private async streamWorkflowGenerator(
    messages: ChatMessage[],
    threadId: string,
    interruptFeedback: string,
    mcpSettings: any,
    observer: Observer<MessageEvent>,
  ): Promise<void> {
    try {
      const config = this.buildWorkflowConfig(threadId, mcpSettings);

      // 处理interrupt feedback或正常流程
      const streamInput = this.determineStreamInput(
        messages,
        interruptFeedback,
      );

      const streamOptions = {
        ...config,
        streamMode: STREAM_MODES,
      };

      // 启动工作流流式处理
      await this.processWorkflowStream(
        streamInput,
        streamOptions,
        threadId,
        observer,
        interruptFeedback ? 'resume' : 'normal',
      );

      observer.complete();
    } catch (error) {
      logger.error(
        `Error in streamWorkflowGenerator: ${error.message}`,
        error.stack,
      );
      observer.error(error);
    }
  }

  /**
   * 构建工作流配置
   */
  private buildWorkflowConfig(
    threadId: string,
    mcpSettings: any,
  ): WorkflowConfig {
    return {
      configurable: {
        thread_id: threadId,
        mcpSettings,
      },
    };
  }

  /**
   * 确定流式输入类型
   */
  private determineStreamInput(
    messages: ChatMessage[],
    interruptFeedback: string,
  ): Command | Record<string, any> {
    // 处理interrupt feedback
    if (interruptFeedback) {
      const resumeMsg = this.buildResumeMessage(messages, interruptFeedback);
      return new Command({ resume: resumeMsg });
    }

    // 正常流程 - 构造初始状态
    return {
      originalPRD:
        messages && messages.length > 0
          ? messages[messages.length - 1].content
          : '',
      structuredPRD: '',
      taskJSON: '',
      humanFeedback: '',
    };
  }

  /**
   * 构建恢复消息
   */
  private buildResumeMessage(
    messages: ChatMessage[],
    interruptFeedback: string,
  ): string {
    let resumeMsg = `[${interruptFeedback}]`;

    // 添加最后一条消息到resume消息中
    if (messages && messages.length > 0) {
      resumeMsg += ` ${messages[messages.length - 1].content}`;
    }

    return resumeMsg;
  }

  /**
   * 处理工作流流式响应
   */
  private async processWorkflowStream(
    streamInput: Command | Record<string, any>,
    streamOptions: WorkflowConfig,
    threadId: string,
    observer: Observer<MessageEvent>,
    mode: 'resume' | 'normal',
  ): Promise<void> {
    const streamGenerator = await this.graph.stream(streamInput, streamOptions);

    for await (const chunk of streamGenerator) {
      console.log(`Received chunk in ${mode} mode:`, chunk);
      try {
        const chunkObj = this.extractChunkObject(chunk);
        if (chunkObj.content === undefined) {
          Object.entries(chunkObj).forEach(([, value]) => {
            if ((value as any).fullResponse) {
              console.log('save assistant message:', chunkObj);

              (value as any).fullResponse &&
                this.saveAssistantMessage(
                  threadId,
                  (value as any).fullResponse,
                );
            }
          });
        }
        const messageEvent = this.processStreamChunk(chunkObj, threadId); // processStreamChunk 现在只负责转换和发送事件

        if (messageEvent && messageEvent.data) {
          observer.next(messageEvent);
        }
      } catch (chunkError) {
        logger.error(
          `Error processing ${mode} chunk: ${chunkError.message}`,
          chunkError.stack,
        );
        // 继续处理下一个chunk，不中断整个流
      }
    }
  }

  /**
   * 提取chunk对象
   */
  private extractChunkObject(chunk: any): any {
    const msg = chunk[chunk.length - 1];
    if (Array.isArray(msg)) {
      return msg[0] || null;
    }
    return msg ?? null;
  }

  /**
   * 处理流式响应块 - 优化版本
   */
  private processStreamChunk(
    chunk: any,
    threadId: string,
  ): MessageEvent | null {
    try {
      if (!chunk || typeof chunk !== 'object') {
        return null;
      }

      // 按优先级处理不同类型的chunk
      const event =
        this.handleInterruptChunk(chunk, threadId) ||
        this.handleContentChunk(chunk, threadId);

      return event;
    } catch (error) {
      logger.error(
        `Error processing stream chunk: ${error.message}`,
        error.stack,
      );
      return null;
    }
  }

  /**
   * 存储助手消息
   */
  private async saveAssistantMessage(
    threadId: string,
    content: string,
    agent?: string,
    messageId?: string, // 可选参数，用于流式消息的ID关联
  ): Promise<Message> {
    try {
      const messageData: Partial<Message> = {
        threadId,
        role: MessageRole.ASSISTANT,
        agent: this.validateAgentType(agent), // 确保 agent 类型有效
        content: content,
      };

      if (messageId) {
        messageData.id = messageId; // 如果提供了 messageId，则使用它
      }
      // 如果 messageId 未提供，TypeORM 在创建记录时会自动生成 ID (例如 UUID)
      // 或者如果主键是自增的，则数据库会自动处理。

      return await this.saveMessage(messageData);
    } catch (error) {
      logger.error(
        `Error saving assistant message: ${error.message}`,
        error.stack,
      );
      throw error; // 抛出错误，Promise 将被拒绝
    }
  }

  /**
   * 验证并转换agent类型
   */
  private validateAgentType(agent?: string): any {
    if (!agent) return undefined;

    // 检查agent是否是有效的AgentType枚举值
    const validAgents = Object.values(MessageRole);
    return validAgents.includes(agent as any) ? agent : undefined;
  }

  /**
   * 处理中断事件chunk
   */
  private handleInterruptChunk(
    chunk: any,
    threadId: string,
  ): MessageEvent | null {
    if (!chunk.__interrupt__) {
      return null;
    }

    const interruptData = chunk.__interrupt__[0];
    const nodeName = interruptData?.ns?.[0] || 'unknown';
    const agent = this.getAgentFromNodeName(nodeName);

    return this.createMessageEvent('interrupt', {
      threadId,
      id: this.generateId(chunk.id),
      role: 'assistant',
      content: JSON.stringify(interruptData?.value || {}),
      agent,
      finish_reason: 'interrupt',
    });
  }

  /**
   * 处理内容chunk (兼容其他格式)
   */
  private handleContentChunk(
    chunk: any,
    threadId: string,
  ): MessageEvent | null {
    if (chunk.content === undefined) {
      return null;
    }

    const agent =
      chunk.agent || this.getAgentFromNodeName(chunk.nodeName) || 'unknown';

    // 使用新的事件构建器
    const event = ChatEventBuilder.messageChunk({
      threadId,
      agent: agent as any,
      role: 'assistant',
      content: chunk.content,
      delta: chunk.content,
      isComplete: chunk.isComplete || false,
    });

    return this.createMessageEventFromChatEvent(event);
  }

  /**
   * 从ChatEvent创建MessageEvent
   */
  private createMessageEventFromChatEvent(chatEvent: any): MessageEvent {
    return {
      type: 'message',
      data: JSON.stringify(chatEvent),
    } as MessageEvent;
  }

  /**
   * 根据节点名称获取对应的 agent 类型
   */
  private getAgentFromNodeName(nodeName: string): string {
    const nodeToAgentMap: Record<string, string> = {
      coordinator: 'coordinator',
      pm: 'planner',
      pmFeedback: 'planner',
      engineer: 'coder',
      engineerFeedback: 'coder',
      finalResponse: 'planner',
      // 可以根据需要添加更多映射
    };

    return nodeToAgentMap[nodeName] || 'coordinator';
  }

  /**
   * 生成唯一ID
   */
  private generateId(chunkId?: string): string {
    return chunkId || uuidv4();
  }

  /**
   * 创建MessageEvent
   */
  private createMessageEvent(eventType: string, data: any): MessageEvent {
    // 清理空的content字段
    if (data.content === '') {
      delete data.content;
    }

    return {
      type: eventType,
      data,
    } as MessageEvent;
  }

  /**
   * 发送任务状态消息
   */
  public sendTaskStatus(
    observer: Observer<MessageEvent>,
    threadId: string,
    agent: string,
    status: 'started' | 'in_progress' | 'completed' | 'failed' | 'paused',
    options: {
      taskId?: string;
      taskName?: string;
      progress?: number;
      message?: string;
      metadata?: Record<string, any>;
    } = {},
  ): void {
    const event = ChatEventBuilder.taskStatus({
      threadId,
      agent: agent as any,
      role: 'assistant',
      status,
      ...options,
    });

    observer.next(this.createMessageEventFromChatEvent(event));
  }

  /**
   * 发送进度更新消息
   */
  public sendProgress(
    observer: Observer<MessageEvent>,
    threadId: string,
    agent: string,
    step: string,
    current: number,
    total: number,
    options: {
      estimatedTimeRemaining?: number;
      message?: string;
    } = {},
  ): void {
    const event = ChatEventBuilder.progress({
      threadId,
      agent: agent as any,
      role: 'assistant',
      step,
      current,
      total,
      percentage: Math.round((current / total) * 100),
      ...options,
    });

    observer.next(this.createMessageEventFromChatEvent(event));
  }

  /**
   * 发送错误消息
   */
  public sendError(
    observer: Observer<MessageEvent>,
    threadId: string,
    agent: string,
    error: string,
    options: {
      code?: string;
      details?: Record<string, any>;
      recoverable?: boolean;
    } = {},
  ): void {
    const event = ChatEventBuilder.error({
      threadId,
      agent: agent as any,
      role: 'assistant',
      error,
      ...options,
    });

    observer.next(this.createMessageEventFromChatEvent(event));
  }

  /**
   * 发送会话结束消息
   */
  public sendEnd(
    observer: Observer<MessageEvent>,
    threadId: string,
    agent: string,
    reason: 'completed' | 'cancelled' | 'error' | 'timeout',
    options: {
      summary?: string;
      results?: Record<string, any>;
      nextActions?: string[];
    } = {},
  ): void {
    const event = ChatEventBuilder.end({
      threadId,
      agent: agent as any,
      role: 'assistant',
      reason,
      ...options,
    });

    observer.next(this.createMessageEventFromChatEvent(event));
  }

  /**
   * 发送通知消息
   */
  public sendNotification(
    observer: Observer<MessageEvent>,
    threadId: string,
    agent: string,
    level: 'info' | 'warning' | 'error' | 'success',
    title: string,
    message: string,
    options: {
      actionable?: boolean;
      actions?: Array<{
        label: string;
        action: string;
        data?: Record<string, any>;
      }>;
    } = {},
  ): void {
    const event = ChatEventBuilder.notification({
      threadId,
      agent: agent as any,
      role: 'assistant',
      level,
      title,
      message,
      ...options,
    });

    observer.next(this.createMessageEventFromChatEvent(event));
  }

  public async getMessagesByThreadId(threadId: string): Promise<Message[]> {
    logger.log(`Fetching messages for threadId: ${threadId}`); // Changed to logger.log assuming it's a general purpose log method
    // Assuming DatabaseService has a method to fetch messages by threadId
    // and sorts them by createdAt in ascending order.
    const messages =
      await this.databaseService.findMessagesByThreadId(threadId);
    if (!messages) {
      return [];
    }
    return messages;
  }

  async updateTaskStatus(
    threadId: string,
    taskId: string,
    newStatus: TaskStatus,
  ): Promise<Task> {
    const task = await this.databaseService.findTaskByIdAndSessionId(
      taskId,
      threadId, // Assuming threadId from controller is the sessionId for the task
    );

    if (!task) {
      throw new NotFoundException(
        `Task with ID "${taskId}" not found in session "${threadId}"`,
      );
    }

    task.status = newStatus;
    const updatedTask = await this.databaseService.saveTask(task);
    return updatedTask;
  }
}
