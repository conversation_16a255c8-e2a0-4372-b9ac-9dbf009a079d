import { Modu<PERSON> } from '@nestjs/common';
import { ChatModule } from './chat/chat.module';
import { GraphService } from '../../plan/graph/graph.service';
import { SessionModule } from './session/session.module';
import { PRDModule } from './prd/prd.module';
import { TaskModule } from './task/task.module';
import { MCPModule } from './mcp/mcp.module';

@Module({
  imports: [ChatModule, SessionModule, PRDModule, TaskModule, MCPModule],
  exports: [ChatModule, SessionModule, PRDModule, TaskModule, MCPModule],
  providers: [GraphService],
})
export class ApisModule {}
