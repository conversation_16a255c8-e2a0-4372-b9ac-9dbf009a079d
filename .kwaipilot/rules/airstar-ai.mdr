---
alwaysApply: true
notes: |
  airstar-ai项目的AI开发规则文件，包含项目架构、开发规范和最佳实践指导。
  查看[规则配置说明](https://docs.corp.kuaishou.com/d/home/<USER>
---

# Airstar-AI 项目开发规则

## 项目概述
Airstar-AI是一个基于LangGraph的智能产品需求文档(PRD)分析和任务规划系统，主要功能包括：
- 智能PRD解析和结构化输出
- 基于LangGraph的多节点工作流编排
- 支持多会话管理和任务状态跟踪
- 文档和图片内容的智能处理
- 前后端分离的现代化架构

## 技术栈架构

### 后端技术栈 (apps/server-ai)
- **框架**: NestJS + TypeScript
- **AI编排**: LangGraph (@langchain/langgraph)
- **LLM集成**: @langchain/openai (通过快手AI Gateway)
- **数据库**: MySQL + TypeORM
- **缓存**: Redis (@infra-node/redis)
- **配置管理**: @nestjs/config + YAML配置
- **API文档**: 支持OpenAPI规范

### 前端技术栈 (apps/client-planner)
- **框架**: React 19 + TypeScript
- **构建工具**: Vite
- **UI组件**: Radix UI + Tailwind CSS
- **状态管理**: Zustand
- **表单处理**: React Hook Form + Zod
- **AI交互**: @ai-sdk/react
- **编辑器**: Monaco Editor + TipTap
- **图表**: @xyflow/react

### 开发工具
- **包管理**: pnpm (锁定版本7.14.0)
- **代码规范**: ESLint + Prettier
- **Git钩子**: Husky + lint-staged
- **构建系统**: Turbo (monorepo管理)

## 核心架构设计

### LangGraph工作流节点
系统采用状态图模式，包含以下核心节点：
- **COORDINATOR**: 协调员节点，负责请求分类和路由
- **TOOLS**: 工具执行节点，处理文档获取等工具调用
- **PRD_PARSER**: PRD解析节点，将原始需求转换为结构化PRD
- **PRD_FEEDBACK**: PRD反馈节点，支持多轮迭代优化
- **PLANNER**: 任务规划节点，基于PRD生成任务列表
- **PLANNER_FEEDBACK**: 规划反馈节点，支持任务调整

### 数据流设计
- 使用流式响应处理AI对话
- 支持中断机制进行人工反馈
- 消息类型包括：message_chunk、interrupt、tools、end
- 状态持久化通过MemorySaver实现

## 开发规范

### 代码组织规范
1. **模块化设计**: 按功能域划分模块(planner、chat、global等)
2. **实体定义**: 使用TypeORM实体类管理数据模型
3. **DTO验证**: 使用class-validator进行请求参数验证
4. **服务分层**: Controller -> Service -> Repository的清晰分层
5. **配置管理**: 环境变量统一通过YAML配置文件管理

### 命名规范
- **枚举定义**: 使用enum定义节点名称和工具名称
- **文件命名**: 采用kebab-case命名方式
- **类命名**: 使用PascalCase，接口以I开头
- **方法命名**: 使用camelCase，布尔值以is/has开头

### AI集成规范
1. **LLM配置**: 统一使用ChatOpenAI通过AI Gateway访问
2. **提示工程**: 采用XML结构化响应格式
3. **工具定义**: 使用@langchain/core/tools定义工具
4. **错误处理**: 实现完整的异常捕获和用户友好提示
5. **流式处理**: 优先使用streaming而非同步调用

### 数据库规范
1. **实体关系**: 合理设计Session、Message、Task等实体关系
2. **索引优化**: 为查询频繁的字段添加索引
3. **数据迁移**: 使用TypeORM migration管理数据库变更
4. **连接池**: 合理配置数据库连接池参数

## 开发最佳实践

### 性能优化
1. **懒加载**: 前端组件和路由采用懒加载
2. **缓存策略**: 合理使用Redis缓存热点数据
3. **分页查询**: 大数据量查询必须实现分页
4. **资源压缩**: 生产环境启用代码压缩和Tree Shaking

### 安全规范
1. **输入验证**: 所有用户输入必须进行验证和清理
2. **CORS配置**: 正确配置跨域访问策略
3. **认证授权**: 集成SSO认证系统
4. **敏感信息**: API密钥等敏感信息通过环境变量管理

### 测试策略
1. **单元测试**: 核心业务逻辑必须编写单元测试
2. **集成测试**: API接口需要集成测试覆盖
3. **E2E测试**: 关键用户流程需要端到端测试
4. **测试覆盖率**: 保持合理的测试覆盖率

### 部署规范
1. **环境隔离**: 开发、测试、生产环境严格隔离
2. **容器化**: 使用Docker进行应用容器化
3. **监控告警**: 集成应用性能监控和错误告警
4. **日志管理**: 结构化日志输出，便于问题排查

## 开发工作流

### 分支管理
- **主干分支**: master (生产环境)
- **版本分支**: release/* (版本发布)
- **功能分支**: feat/* (新功能开发)
- **修复分支**: fix/* (问题修复)
- **热修复**: hotfix/* (紧急修复)

### 代码提交
1. 提交前必须通过lint检查
2. 提交信息遵循约定式提交规范
3. 大功能开发需要拆分为小的提交
4. 合并前必须通过代码审查

### 发布流程
1. 功能开发完成后提交PR
2. 通过代码审查和自动化测试
3. 合并到版本分支进行集成测试
4. 测试通过后合并到主干分支
5. 自动化部署到生产环境

## 故障排查指南

### 常见问题
1. **LangGraph执行异常**: 检查节点配置和状态传递
2. **数据库连接问题**: 验证连接配置和网络连通性
3. **AI Gateway调用失败**: 检查API密钥和网络配置
4. **前端构建失败**: 检查依赖版本和构建配置

### 调试技巧
1. 启用详细日志输出
2. 使用开发者工具调试前端问题
3. 利用NestJS内置的调试功能
4. 通过单元测试隔离问题

## 扩展指南

### 新增AI节点
1. 在NodeNames枚举中定义节点名称
2. 实现节点处理函数
3. 在图构建中添加节点和边
4. 更新状态注解定义

### 新增工具集成
1. 在ToolNames枚举中定义工具名称
2. 使用@langchain/core/tools定义工具
3. 在工具节点中添加处理逻辑
4. 更新工具调用文档

### 数据库扩展
1. 创建新的实体类
2. 定义实体关系
3. 生成并执行迁移
4. 更新相关服务逻辑

这些规则旨在确保airstar-ai项目的代码质量、可维护性和团队协作效率。所有开发者都应该遵循这些规范，并在实践中不断完善和优化。
